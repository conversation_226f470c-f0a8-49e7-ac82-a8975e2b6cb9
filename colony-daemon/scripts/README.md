# Colony Daemon Scripts

This directory contains example scripts for testing and demonstrating the Colony Daemon REST API.

## Scripts

### `example.sh`

A comprehensive test script that demonstrates the new asynchronous job-based API endpoints.

**Features:**
- Tests all async job endpoints (cache refresh, upload all, refresh ref, search, get subject data)
- Demonstrates proper job polling with status and progress updates
- Shows how to retrieve job results when completed
- Includes error handling for failed jobs and non-existent jobs
- Compares async endpoints with legacy synchronous endpoints

**Usage:**
```bash
# Make sure the colony-daemon is running on localhost:3000
./scripts/example.sh
```

**Requirements:**
- `curl` - for making HTTP requests
- `jq` - for JSON parsing and formatting
- Colony daemon running on `http://localhost:3000`

**What it tests:**
1. JWT token authentication
2. Health check endpoint
3. Async cache refresh job
4. Pod creation (synchronous)
5. Async refresh pod references job
6. Async upload all pods job
7. Subject data operations (mixed sync/async)
8. Async search job
9. Error handling for non-existent jobs
10. Comparison with legacy synchronous endpoints

**Sample Output:**
```
🚀 Testing Colony Daemon Async REST API
========================================
📝 Getting JWT token...
✅ Token obtained: eyJ0eXAiOiJKV1QiLCJh...

🏥 Testing health check...
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "0.1.0"
}

🔄 Testing async cache refresh...
{
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "pending",
  "message": "Cache refresh job started",
  "timestamp": "2024-01-01T12:00:00Z"
}
⏳ Polling job 550e8400-e29b-41d4-a716-446655440000 for Cache Refresh...
   Status: running, Progress: 0.1, Message: Starting cache refresh
   Status: completed, Progress: 1.0, Message: Starting cache refresh
✅ Cache Refresh completed successfully!
```

## Key Benefits Demonstrated

1. **No Client Timeouts**: Long-running operations return immediately with a job ID
2. **Progress Tracking**: Real-time progress updates during job execution
3. **Error Handling**: Clear error messages and status codes
4. **Mutual Exclusion**: Only one long-running operation at a time
5. **Backward Compatibility**: Legacy endpoints still work

## Customization

You can modify the script to:
- Change the base URL (`BASE_URL` variable)
- Adjust polling intervals (change `sleep 2` in the `poll_job` function)
- Test different search queries or pod configurations
- Add additional error handling or logging

## Troubleshooting

**"Failed to get JWT token"**
- Make sure the colony-daemon is running
- Check that the daemon is accessible at the configured URL

**"Job creation failed"**
- Another operation might already be running
- Check the daemon logs for more details

**Jobs stuck in "running" state**
- Check daemon logs for errors
- The underlying colonylib operation might be taking longer than expected
