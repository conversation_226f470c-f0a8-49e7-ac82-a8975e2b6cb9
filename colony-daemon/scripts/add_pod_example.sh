#!/bin/bash

# Function to poll job status until completion
poll_job() {
    local job_id=$1
    local operation_name=$2
    echo "⏳ Polling job $job_id for $operation_name..."

    while true; do
        response=$(curl -s -X GET "$BASE_URL/api/v1/jobs/$job_id" \
            -H "Authorization: Bearer $TOKEN")

        status=$(echo $response | jq -r '.job.status')
        progress=$(echo $response | jq -r '.job.progress // 0')
        message=$(echo $response | jq -r '.job.message // ""')

        echo "   Status: $status, Progress: $(printf "%.1f" $progress), Message: $message"

        if [ "$status" = "completed" ] || [ "$status" = "failed" ]; then
            break
        fi

        sleep 20
    done

    if [ "$status" = "completed" ]; then
        echo "✅ $operation_name completed successfully!"
        # Get the result
        echo "📋 Getting result for $operation_name..."
        curl -s -X GET "$BASE_URL/api/v1/jobs/$job_id/result" \
            -H "Authorization: Bearer $TOKEN" | jq
    else
        echo "❌ $operation_name failed!"
        echo $response | jq
    fi
}

# Colony Daemon API Test Script
BASE_URL="http://localhost:3000"

echo "🚀 Testing Colony Daemon REST API"
echo "=================================="

# Get JWT token
echo "📝 Getting JWT token..."
TOKEN=$(curl -s -X POST $BASE_URL/auth/token -H "Content-Type: application/json" | jq -r '.token')
if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ Failed to get JWT token"
    exit 1
fi
echo "✅ Token obtained: ${TOKEN:0:20}..."

# Health check
echo -e "\n🏥 Testing health check..."
curl -s -X GET $BASE_URL/health | jq

## Refresh cache
#echo -e "\n🔄 Testing cache refresh..."
#curl -s -X POST $BASE_URL/api/v1/cache \
#  -H "Authorization: Bearer $TOKEN" | jq

# Add a pod
echo -e "\n➕ Testing add pod..."
POD_RESPONSE=$(curl -s -X POST $BASE_URL/api/v1/pods \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "test-pod-'$(date +%s)'",
    "description": "Test pod created by curl",
    "metadata": {"test": true}
  }')
echo $POD_RESPONSE | jq
POD_ID=$(echo $POD_RESPONSE | jq -r '.id')

# Upload all pods
echo -e "\n⬆️ Testing async upload all pods..."
upload_response=$(curl -s -X POST $BASE_URL/api/v1/jobs/cache/upload \
  -H "Authorization: Bearer $TOKEN")
echo $upload_response | jq
upload_job_id=$(echo $upload_response | jq -r '.job_id')

if [ "$upload_job_id" != "null" ] && [ ! -z "$upload_job_id" ]; then
    poll_job $upload_job_id "Upload All Pods"
fi

echo -e "\n✅ All tests completed!"