# colony-utils

A collection of utilities for interacting with the [colonylib](https://github.com/zettawatt/colonylib) metatdata framework for the [Autonomi](https://autonomi.com) decentralized network.

## Components
**[colony-daemon](colony-daemon)** Server that implements the colonylib public APIs
- Creates a colonylib PodManager instance in a daemon configuration
- Provides a REST endpoint for all colonylib operations
**[colony-cli](colony-cli)** Command line tool for interacting with the daemon

## License

This project is licensed under the GPL-3.0-only License - see the [LICENSE](LICENSE) file for details.

## Links

- **Autonomi Network**: [autonomi.com](https://autonomi.com)
- **colonylib**: [github.com/zettawatt/colonylib](https://github.com/zettawatt/colonylib)
